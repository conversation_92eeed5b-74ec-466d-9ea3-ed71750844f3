{% extends "base.html" %}

{% block title %}Admin Dashboard{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h3 mb-0">
                <i class="bi bi-speedometer2"></i> Admin Dashboard
            </h1>
            <p class="text-muted">Manage parking lots, spots, and view analytics</p>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ total_lots }}</h4>
                            <p class="card-text">Parking Lots</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-building display-4"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ available_spots }}</h4>
                            <p class="card-text">Available Spots</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-check-circle display-4"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ occupied_spots }}</h4>
                            <p class="card-text">Occupied Spots</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-x-circle display-4"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ total_users }}</h4>
                            <p class="card-text">Registered Users</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-people display-4"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-bar-chart"></i> Parking Lot Occupancy Chart
                    </h5>
                </div>
                <div class="card-body">
                    <div id="chartContainer" class="text-center">
                        <button type="button" class="btn btn-primary" onclick="loadChart()">
                            <i class="bi bi-graph-up"></i> Load Chart
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Create New Parking Lot -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-plus-circle"></i> Create New Parking Lot
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('create_parking_lot') }}" novalidate>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="location_name" class="form-label">Location Name</label>
                                    <input type="text" class="form-control" id="location_name" name="location_name" required>
                                    <div class="invalid-feedback">Please provide a location name.</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="pin_code" class="form-label">Pin Code</label>
                                    <input type="text" class="form-control" id="pin_code" name="pin_code" required pattern="[0-9]{6}">
                                    <div class="invalid-feedback">Please provide a valid 6-digit pin code.</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="address" class="form-label">Address</label>
                            <textarea class="form-control" id="address" name="address" rows="2" required></textarea>
                            <div class="invalid-feedback">Please provide an address.</div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="price_per_hour" class="form-label">Price per Hour (₹)</label>
                                    <input type="number" class="form-control" id="price_per_hour" name="price_per_hour" min="1" step="0.01" required>
                                    <div class="invalid-feedback">Please provide a valid price.</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="maximum_spots" class="form-label">Maximum Spots</label>
                                    <input type="number" class="form-control" id="maximum_spots" name="maximum_spots" min="1" max="1000" required>
                                    <div class="invalid-feedback">Please provide a valid number of spots (1-1000).</div>
                                </div>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-success">
                            <i class="bi bi-plus-circle"></i> Create Parking Lot
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Existing Parking Lots -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-building"></i> Manage Parking Lots
                    </h5>
                </div>
                <div class="card-body">
                    {% if parking_lots %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Location</th>
                                    <th>Address</th>
                                    <th>Pin Code</th>
                                    <th>Price/Hour</th>
                                    <th>Total Spots</th>
                                    <th>Available</th>
                                    <th>Occupied</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for lot in parking_lots %}
                                <tr>
                                    <td><strong>{{ lot.location_name }}</strong></td>
                                    <td>{{ lot.address[:50] }}{% if lot.address|length > 50 %}...{% endif %}</td>
                                    <td>{{ lot.pin_code }}</td>
                                    <td>₹{{ lot.price_per_hour }}</td>
                                    <td>{{ lot.maximum_spots }}</td>
                                    <td><span class="badge bg-success">{{ lot.get_available_spots_count() }}</span></td>
                                    <td><span class="badge bg-danger">{{ lot.get_occupied_spots_count() }}</span></td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#editModal{{ lot.id }}">
                                            <i class="bi bi-pencil"></i> Edit
                                        </button>
                                        <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal{{ lot.id }}">
                                            <i class="bi bi-trash"></i> Delete
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-building display-4 text-muted"></i>
                        <p class="text-muted mt-2">No parking lots created yet. Create your first parking lot above.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit Modals -->
{% for lot in parking_lots %}
<div class="modal fade" id="editModal{{ lot.id }}" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Parking Lot</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('edit_parking_lot', lot_id=lot.id) }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Location Name</label>
                        <input type="text" class="form-control" name="location_name" value="{{ lot.location_name }}" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Address</label>
                        <textarea class="form-control" name="address" rows="2" required>{{ lot.address }}</textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Pin Code</label>
                                <input type="text" class="form-control" name="pin_code" value="{{ lot.pin_code }}" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Price per Hour (₹)</label>
                                <input type="number" class="form-control" name="price_per_hour" value="{{ lot.price_per_hour }}" step="0.01" required>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Maximum Spots</label>
                        <input type="number" class="form-control" name="maximum_spots" value="{{ lot.maximum_spots }}" required>
                        <div class="form-text">Current: {{ lot.maximum_spots }} spots</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Parking Lot</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal{{ lot.id }}" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Delete Parking Lot</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the parking lot <strong>"{{ lot.location_name }}"</strong>?</p>
                <p class="text-danger">
                    <i class="bi bi-exclamation-triangle"></i>
                    This action cannot be undone and will only work if all spots are empty.
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="POST" action="{{ url_for('delete_parking_lot', lot_id=lot.id) }}" class="d-inline">
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endfor %}
{% endblock %}

{% block extra_scripts %}
<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByTagName('form');
        Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Load chart function
function loadChart() {
    fetch('/admin/chart')
        .then(response => response.json())
        .then(data => {
            if (data.chart) {
                document.getElementById('chartContainer').innerHTML =
                    '<img src="data:image/png;base64,' + data.chart + '" class="img-fluid" alt="Parking Chart">';
            } else {
                document.getElementById('chartContainer').innerHTML =
                    '<p class="text-muted">No data available for chart</p>';
            }
        })
        .catch(error => {
            console.error('Error loading chart:', error);
            document.getElementById('chartContainer').innerHTML =
                '<p class="text-danger">Error loading chart</p>';
        });
}
</script>
{% endblock %}
