{% extends "base.html" %}

{% block title %}User Dashboard - Vehicle Parking App{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h3 mb-0">
                <i class="bi bi-speedometer2"></i> Welcome, {{ user.username }}!
            </h1>
            <p class="text-muted">Manage your parking reservations and view your history</p>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ reservations|selectattr("status", "equalto", "booked")|list|length + reservations|selectattr("status", "equalto", "parked")|list|length }}</h4>
                            <p class="card-text">Active Reservations</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-car-front display-4"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ reservations|selectattr("status", "equalto", "completed")|list|length }}</h4>
                            <p class="card-text">Completed Trips</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-check-circle display-4"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">₹{{ "%.2f"|format(reservations|selectattr("status", "equalto", "completed")|map(attribute="parking_cost")|sum) }}</h4>
                            <p class="card-text">Total Spent</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-currency-rupee display-4"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-bar-chart"></i> Your Parking Cost History
                    </h5>
                </div>
                <div class="card-body">
                    <div id="chartContainer" class="text-center">
                        {% if user_chart %}
                            <img src="data:image/png;base64,{{ user_chart }}" class="img-fluid" alt="Parking Cost Chart" style="max-width: 100%; height: auto;">
                        {% else %}
                            <div class="text-center py-4">
                                <i class="bi bi-bar-chart text-muted fs-1"></i>
                                <p class="text-muted mt-2">No parking history available</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Book New Parking -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-plus-circle"></i> Book Parking Spot
                    </h5>
                </div>
                <div class="card-body">
                    {% if reservations|selectattr("status", "in", ["booked", "parked"])|list|length == 0 %}
                    <form method="POST" action="{{ url_for('book_parking_spot') }}" novalidate>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="lot_id" class="form-label">Select Parking Lot</label>
                                    <select class="form-select" id="lot_id" name="lot_id" required>
                                        <option value="">Choose a parking lot...</option>
                                        {% for lot in parking_lots %}
                                        {% if lot.get_available_spots_count() > 0 %}
                                        <option value="{{ lot.id }}">
                                            {{ lot.location_name }} - ₹{{ lot.price_per_hour }}/hr
                                            ({{ lot.get_available_spots_count() }} available)
                                        </option>
                                        {% endif %}
                                        {% endfor %}
                                    </select>
                                    <div class="invalid-feedback">Please select a parking lot.</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="vehicle_number" class="form-label">Vehicle Number</label>
                                    <input type="text"
                                           class="form-control"
                                           id="vehicle_number"
                                           name="vehicle_number"
                                           required
                                           pattern="[A-Z]{2}[0-9]{2}[A-Z]{2}[0-9]{4}"
                                           placeholder="e.g., MH12AB1234"
                                           style="text-transform: uppercase;">
                                    <div class="invalid-feedback">Please provide a valid vehicle number (e.g., MH12AB1234).</div>
                                </div>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-success">
                            <i class="bi bi-plus-circle"></i> Book Parking Spot
                        </button>
                    </form>
                    {% else %}
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle"></i>
                        You already have an active reservation. Please complete or cancel your current reservation before booking a new one.
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Current Reservations -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-list-ul"></i> Your Reservations
                    </h5>
                </div>
                <div class="card-body">
                    {% if reservations %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Parking Lot</th>
                                    <th>Spot</th>
                                    <th>Vehicle</th>
                                    <th>Status</th>
                                    <th>Booked At</th>
                                    <th>Parked At</th>
                                    <th>Left At</th>
                                    <th>Cost</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for reservation in reservations %}
                                <tr>
                                    <td><strong>{{ reservation.parking_spot.parking_lot.location_name }}</strong></td>
                                    <td>{{ reservation.parking_spot.spot_number }}</td>
                                    <td>{{ reservation.vehicle_number }}</td>
                                    <td>
                                        {% if reservation.status == 'booked' %}
                                            <span class="badge bg-warning">Booked</span>
                                        {% elif reservation.status == 'parked' %}
                                            <span class="badge bg-info">Parked</span>
                                        {% elif reservation.status == 'completed' %}
                                            <span class="badge bg-success">Completed</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% set booking_time = reservation.get_booking_time_ist() %}
                                        {% if booking_time %}
                                            {{ booking_time.strftime('%d/%m/%Y %H:%M IST') }}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% set parking_time = reservation.get_parking_time_ist() %}
                                        {% if parking_time %}
                                            {{ parking_time.strftime('%d/%m/%Y %H:%M IST') }}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% set leaving_time = reservation.get_leaving_time_ist() %}
                                        {% if leaving_time %}
                                            {{ leaving_time.strftime('%d/%m/%Y %H:%M IST') }}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if reservation.parking_cost > 0 %}
                                            ₹{{ "%.2f"|format(reservation.parking_cost) }}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if reservation.status == 'booked' %}
                                            <form method="POST" action="{{ url_for('park_vehicle', reservation_id=reservation.id) }}" class="d-inline">
                                                <button type="submit" class="btn btn-sm btn-success" title="Check In">
                                                    <i class="bi bi-play-circle"></i> Park
                                                </button>
                                            </form>
                                            <form method="POST" action="{{ url_for('release_parking_spot', reservation_id=reservation.id) }}" class="d-inline">
                                                <button type="submit" class="btn btn-sm btn-danger" title="Cancel Booking">
                                                    <i class="bi bi-x-circle"></i> Cancel
                                                </button>
                                            </form>
                                        {% elif reservation.status == 'parked' %}
                                            <form method="POST" action="{{ url_for('release_parking_spot', reservation_id=reservation.id) }}" class="d-inline">
                                                <button type="submit" class="btn btn-sm btn-warning" title="Check Out">
                                                    <i class="bi bi-stop-circle"></i> Leave
                                                </button>
                                            </form>
                                        {% elif reservation.status == 'completed' %}
                                            <span class="text-muted">Completed</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-car-front display-4 text-muted"></i>
                        <p class="text-muted mt-2">No reservations yet. Book your first parking spot above!</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<!-- No JavaScript needed - charts are rendered server-side -->
{% endblock %}