{% extends "base.html" %}

{% block title %}Edit Profile{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="bi bi-pencil-square"></i> Edit Profile
                    </h1>
                    <p class="text-muted">Update your account information</p>
                </div>
                <div>
                    <a href="{{ url_for('view_profile') }}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left me-2"></i>Back to Profile
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Profile Form -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-person-gear me-2"></i>Account Information
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('edit_profile') }}" novalidate>
                        <!-- Basic Information -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="username" class="form-label">
                                    <i class="bi bi-person me-1"></i>Username <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       value="{{ user.username }}" required>
                                <div class="form-text">Choose a unique username for your account</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">
                                    <i class="bi bi-envelope me-1"></i>Email Address <span class="text-danger">*</span>
                                </label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="{{ user.email }}" required>
                                <div class="form-text">We'll never share your email with anyone else</div>
                            </div>
                        </div>

                        <!-- Account Type Display -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">
                                    <i class="bi bi-shield-check me-1"></i>Account Type
                                </label>
                                <div class="form-control-plaintext bg-light rounded p-2">
                                    {% if user.is_admin %}
                                        <span class="badge bg-warning text-dark">Administrator</span>
                                        <small class="text-muted ms-2">Full system access</small>
                                    {% else %}
                                        <span class="badge bg-success">Regular User</span>
                                        <small class="text-muted ms-2">Standard parking access</small>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <hr class="my-4">

                        <!-- Password Change Section -->
                        <h6 class="text-muted mb-3">
                            <i class="bi bi-key me-2"></i>Change Password (Optional)
                        </h6>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="current_password" class="form-label">Current Password</label>
                                <input type="password" class="form-control" id="current_password" name="current_password">
                                <div class="form-text">Required only if changing password</div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="new_password" class="form-label">New Password</label>
                                <input type="password" class="form-control" id="new_password" name="new_password">
                                <div class="form-text">Minimum 6 characters</div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="confirm_password" class="form-label">Confirm New Password</label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                                <div class="form-text">Must match new password</div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                            <a href="{{ url_for('view_profile') }}" class="btn btn-outline-secondary me-md-2">
                                <i class="bi bi-x-circle me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle me-2"></i>Save Changes
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Security Notice -->
            <div class="card shadow-sm mt-4">
                <div class="card-header bg-warning text-dark">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-shield-exclamation me-2"></i>Security Notice
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-muted">Password Security</h6>
                            <ul class="list-unstyled small">
                                <li><i class="bi bi-check-circle text-success me-2"></i>Use at least 6 characters</li>
                                <li><i class="bi bi-check-circle text-success me-2"></i>Include numbers and letters</li>
                                <li><i class="bi bi-check-circle text-success me-2"></i>Avoid common passwords</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted">Account Protection</h6>
                            <ul class="list-unstyled small">
                                <li><i class="bi bi-check-circle text-success me-2"></i>Keep your email updated</li>
                                <li><i class="bi bi-check-circle text-success me-2"></i>Use a unique username</li>
                                <li><i class="bi bi-check-circle text-success me-2"></i>Log out from shared devices</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByTagName('form');
        Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                // Custom password validation
                var newPassword = document.getElementById('new_password').value;
                var confirmPassword = document.getElementById('confirm_password').value;
                var currentPassword = document.getElementById('current_password').value;
                
                // If new password is provided, validate
                if (newPassword) {
                    if (!currentPassword) {
                        document.getElementById('current_password').setCustomValidity('Current password is required when changing password');
                    } else {
                        document.getElementById('current_password').setCustomValidity('');
                    }
                    
                    if (newPassword !== confirmPassword) {
                        document.getElementById('confirm_password').setCustomValidity('Passwords do not match');
                    } else {
                        document.getElementById('confirm_password').setCustomValidity('');
                    }
                    
                    if (newPassword.length < 6) {
                        document.getElementById('new_password').setCustomValidity('Password must be at least 6 characters long');
                    } else {
                        document.getElementById('new_password').setCustomValidity('');
                    }
                } else {
                    // Clear validations if no new password
                    document.getElementById('current_password').setCustomValidity('');
                    document.getElementById('new_password').setCustomValidity('');
                    document.getElementById('confirm_password').setCustomValidity('');
                }
                
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
        
        // Real-time password confirmation check
        document.getElementById('confirm_password').addEventListener('input', function() {
            var newPassword = document.getElementById('new_password').value;
            var confirmPassword = this.value;
            
            if (newPassword && confirmPassword && newPassword !== confirmPassword) {
                this.setCustomValidity('Passwords do not match');
            } else {
                this.setCustomValidity('');
            }
        });
    }, false);
})();
</script>
{% endblock %}
