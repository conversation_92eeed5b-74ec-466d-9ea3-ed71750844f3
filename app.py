from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, timezone
import pytz
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
from flask_restful import Api

# Create Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'this-is-my-project.'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///myproject.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['SQLALCHEMY_ENGINE_OPTIONS'] = {
    'pool_pre_ping': True,
    'pool_recycle': 300,
    'connect_args': {'check_same_thread': False}
}

# Initialize extensions
db = SQLAlchemy(app)
api = Api(app)

# Define models directly here to avoid circular imports
class User(db.Model):
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    is_admin = db.Column(db.Boolean, default=False, nullable=False)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))

    # Relationship with reservations
    reservations = db.relationship('Reservation', backref='user', lazy=True, cascade='all, delete-orphan')

    def __repr__(self):
        return f'<User {self.username}>'

class ParkingLot(db.Model):
    __tablename__ = 'parking_lots'

    id = db.Column(db.Integer, primary_key=True)
    location_name = db.Column(db.String(100), nullable=False)
    address = db.Column(db.Text, nullable=False)
    pin_code = db.Column(db.String(10), nullable=False)
    price_per_hour = db.Column(db.Float, nullable=False)
    maximum_spots = db.Column(db.Integer, nullable=False)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))

    # Relationship with parking spots
    parking_spots = db.relationship('ParkingSpot', backref='parking_lot', lazy=True, cascade='all, delete-orphan')

    def __repr__(self):
        return f'<ParkingLot {self.location_name}>'

    def get_available_spots_count(self):
        return ParkingSpot.query.filter_by(lot_id=self.id, status='A').count()

    def get_occupied_spots_count(self):
        return ParkingSpot.query.filter_by(lot_id=self.id, status='O').count()

class ParkingSpot(db.Model):
    __tablename__ = 'parking_spots'

    id = db.Column(db.Integer, primary_key=True)
    lot_id = db.Column(db.Integer, db.ForeignKey('parking_lots.id'), nullable=False)
    spot_number = db.Column(db.String(10), nullable=False)
    status = db.Column(db.String(1), default='A', nullable=False)  # A=Available, O=Occupied
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))

    # Relationship with reservations
    reservations = db.relationship('Reservation', backref='parking_spot', lazy=True, cascade='all, delete-orphan')

    def __repr__(self):
        return f'<ParkingSpot {self.spot_number} - {self.status}>'

class Reservation(db.Model):
    __tablename__ = 'reservations'

    id = db.Column(db.Integer, primary_key=True)
    spot_id = db.Column(db.Integer, db.ForeignKey('parking_spots.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    vehicle_number = db.Column(db.String(20), nullable=False)
    parking_timestamp = db.Column(db.DateTime, nullable=True)
    leaving_timestamp = db.Column(db.DateTime, nullable=True)
    parking_cost = db.Column(db.Float, default=0.0)
    status = db.Column(db.String(20), default='booked')  # booked, parked, completed
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))

    def __repr__(self):
        return f'<Reservation {self.vehicle_number} - {self.status}>'

    def get_booking_time_ist(self):
        """Get booking time in IST format"""
        if self.created_at:
            ist = pytz.timezone('Asia/Kolkata')
            if self.created_at.tzinfo is None:
                return ist.localize(self.created_at)
            else:
                return self.created_at.astimezone(ist)
        return None

    def get_parking_time_ist(self):
        """Get parking time in IST format"""
        if self.parking_timestamp:
            ist = pytz.timezone('Asia/Kolkata')
            if self.parking_timestamp.tzinfo is None:
                return ist.localize(self.parking_timestamp)
            else:
                return self.parking_timestamp.astimezone(ist)
        return None

    def get_leaving_time_ist(self):
        """Get leaving time in IST format"""
        if self.leaving_timestamp:
            ist = pytz.timezone('Asia/Kolkata')
            if self.leaving_timestamp.tzinfo is None:
                return ist.localize(self.leaving_timestamp)
            else:
                return self.leaving_timestamp.astimezone(ist)
        return None

    def calculate_cost(self):
        if self.parking_timestamp and self.leaving_timestamp:
            # Get IST timezone
            ist = pytz.timezone('Asia/Kolkata')

            # Convert to IST if timestamps are in UTC
            if self.parking_timestamp.tzinfo is None:
                parking_time = ist.localize(self.parking_timestamp)
            else:
                parking_time = self.parking_timestamp.astimezone(ist)

            if self.leaving_timestamp.tzinfo is None:
                leaving_time = ist.localize(self.leaving_timestamp)
            else:
                leaving_time = self.leaving_timestamp.astimezone(ist)

            # Calculate duration in hours
            duration = (leaving_time - parking_time).total_seconds() / 3600

            # Get parking lot price
            parking_lot = db.session.get(ParkingLot, self.parking_spot.lot_id)
            cost = duration * parking_lot.price_per_hour

            return round(cost, 2)
        return 0.0

# Now import controllers - they will have access to the models defined above
import sys
sys.modules['application.models'] = sys.modules[__name__]  # Make models available as application.models

# Import controllers
exec(open('application/controllers.py').read())

# Create database tables
with app.app_context():
    db.create_all()

    # Create admin user if not exists
    admin = User.query.filter_by(username='admin').first()
    if not admin:
        admin_user = User(
            username='admin',
            email='<EMAIL>',
            password_hash=generate_password_hash('admin123'),
            is_admin=True
        )
        db.session.add(admin_user)
        db.session.commit()
        print("Admin user created: username='admin', password='admin123'")

if __name__ == '__main__':
    app.run(debug=True)