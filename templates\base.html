<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Welcome to Vehicle Parking Website {% endblock %}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='style.css') }}" rel="stylesheet">

    <!-- Custom CSS for z-index fixes -->
    <style>
        /* Fix navbar and dropdown z-index issues */
        .navbar {
            z-index: 1030 !important;
        }

        .dropdown-menu {
            z-index: 1040 !important;
            border: 1px solid rgba(0, 0, 0, 0.15);
            border-radius: 0.375rem;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            background-color: #ffffff;
            min-width: 200px;
        }

        /* Ensure main content doesn't overlap navbar */
        main {
            position: relative;
            z-index: 1;
        }

        /* Fix for dropdown appearing behind content */
        .nav-item.dropdown {
            position: relative;
        }

        .dropdown-menu-end {
            right: 0 !important;
            left: auto !important;
        }

        /* Enhanced dropdown styling */
        .dropdown-item {
            padding: 0.5rem 1rem;
            color: #212529;
            text-decoration: none;
            background-color: transparent;
            border: 0;
            display: block;
            width: 100%;
            clear: both;
            font-weight: 400;
            line-height: 1.5;
            white-space: nowrap;
        }

        .dropdown-item:hover,
        .dropdown-item:focus {
            color: #1e2125;
            background-color: #e9ecef;
        }

        .dropdown-divider {
            height: 0;
            margin: 0.5rem 0;
            overflow: hidden;
            border-top: 1px solid rgba(0, 0, 0, 0.15);
        }
    </style>

    {% block extra_head %}{% endblock %}
</head>
<body class="d-flex flex-column min-vh-100">
    {% if session.user_id and request.endpoint != 'index' %}
    <!-- Navigation Bar (only show when logged in and not on home page) -->
    <nav class="navbar navbar-expand-lg navbar-dark shadow-sm" style="background-color: #10b981;">
        <div class="container-fluid px-4">
            <a class="navbar-brand fw-bold" href="{{ url_for('admin_dashboard') if session.is_admin else url_for('user_dashboard') }}">
            <!--    <i class="bi bi-car-front-fill me-2"></i> -->
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    {% if session.is_admin %}
                    <li class="nav-item">
                        <a class="nav-link {{ 'active' if request.endpoint == 'admin_dashboard' else '' }}" href="{{ url_for('admin_dashboard') }}">
                            <i class="bi bi-speedometer2 me-1"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ 'active' if request.endpoint == 'view_users' else '' }}" href="{{ url_for('view_users') }}">
                            <i class="bi bi-people me-1"></i> Users
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ 'active' if request.endpoint == 'search_parking_spot' else '' }}" href="{{ url_for('search_parking_spot') }}">
                            <i class="bi bi-search me-1"></i> Search Spots
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-gear me-1"></i> Manage
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('admin_dashboard') }}">
                                <i class="bi bi-building me-2"></i> Parking Lots
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/admin/reports">
                                <i class="bi bi-graph-up me-2"></i> Reports
                            </a></li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link {{ 'active' if request.endpoint == 'user_dashboard' else '' }}" href="{{ url_for('user_dashboard') }}">
                            <i class="bi bi-speedometer2 me-1"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('user_dashboard') }}#book-parking">
                            <i class="bi bi-plus-circle me-1"></i> Book Parking
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('user_dashboard') }}#my-reservations">
                            <i class="bi bi-list-ul me-1"></i> My Reservations
                        </a>
                    </li>
                    {% endif %}
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-person-circle me-2"></i>
                            <span class="d-none d-md-inline">{{ session.username }}</span>
                            {% if session.is_admin %}
                                <span class="badge bg-warning text-dark ms-2 d-none d-md-inline">Admin</span>
                            {% endif %}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><h6 class="dropdown-header">
                                <i class="bi bi-person me-2"></i>{{ session.username }}
                                {% if session.is_admin %}<span class="badge bg-warning text-dark ms-2">Admin</span>{% endif %}
                            </h6></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('view_profile') }}">
                                <i class="bi bi-person-gear me-2"></i> View/Edit Profile
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('logout') }}">
                                <i class="bi bi-box-arrow-right me-2"></i> Logout
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    {% endif %}

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="container-fluid px-4 mt-3">
                {% for category, message in messages %}
                    {% set alert_class = {
                        'error': 'danger',
                        'success': 'success',
                        'warning': 'warning',
                        'info': 'info'
                    }.get(category, 'primary') %}
                    <div class="alert alert-{{ alert_class }} alert-dismissible fade show shadow-sm" role="alert">
                        <i class="bi bi-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' if category == 'success' else 'info-circle' }}"></i>
                        <strong>{{ category.title() }}:</strong> {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <!-- Main Content -->
    <main class="flex-grow-1">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-light text-center py-3 mt-auto">
        <div class="container">
            <p class="mb-0">&copy; 2025 Vehicle Parking App V1. All rights reserved.</p>
        </div>
    </footer>

    <!-- Bootstrap JS (for UI components like dropdowns and alerts) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Flash Message Auto-Dismiss Script -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-dismiss flash messages after specified time
        const alerts = document.querySelectorAll('.alert[role="alert"]');
        const flashContainer = document.querySelector('.container-fluid.px-4.mt-3');

        // Function to check if container should be removed
        function checkAndRemoveContainer() {
            if (flashContainer) {
                const remainingAlerts = flashContainer.querySelectorAll('.alert[role="alert"]');
                if (remainingAlerts.length === 0) {
                    // Fade out the container
                    flashContainer.style.transition = 'opacity 0.3s ease-out, margin 0.3s ease-out, padding 0.3s ease-out';
                    flashContainer.style.opacity = '0';
                    flashContainer.style.marginTop = '0';
                    flashContainer.style.paddingTop = '0';
                    flashContainer.style.paddingBottom = '0';

                    // Remove container after fade animation
                    setTimeout(function() {
                        if (flashContainer && flashContainer.parentNode) {
                            flashContainer.remove();
                        }
                    }, 300);
                }
            }
        }

        alerts.forEach(function(alert) {
            // Get alert type to determine timing
            const isError = alert.classList.contains('alert-danger');
            const isWarning = alert.classList.contains('alert-warning');
            const isSuccess = alert.classList.contains('alert-success');
            const isInfo = alert.classList.contains('alert-info');

            // Set different timings based on message type
            let dismissTime;
            if (isError) {
                dismissTime = 8000; // 8 seconds for errors (important to read)
            } else if (isWarning) {
                dismissTime = 6000; // 6 seconds for warnings
            } else if (isSuccess) {
                dismissTime = 4000; // 4 seconds for success messages
            } else {
                dismissTime = 5000; // 5 seconds for info/other messages
            }

            // Add progress bar for visual countdown
            const progressBar = document.createElement('div');
            progressBar.className = 'alert-progress-bar';
            progressBar.style.cssText = `
                position: absolute;
                bottom: 0;
                left: 0;
                height: 3px;
                background: rgba(255, 255, 255, 0.7);
                width: 100%;
                transform-origin: left;
                animation: alertProgress ${dismissTime}ms linear forwards;
            `;

            // Make alert relative positioned for progress bar
            alert.style.position = 'relative';
            alert.style.overflow = 'hidden';
            alert.appendChild(progressBar);

            // Auto-dismiss after specified time
            const timeoutId = setTimeout(function() {
                try {
                    if (alert && alert.parentNode && document.contains(alert)) {
                        // Smoothly fade out the alert
                        alert.style.transition = 'opacity 0.3s ease-out, transform 0.3s ease-out';
                        alert.style.opacity = '0';
                        alert.style.transform = 'translateY(-10px)';

                        // Remove the alert after fade animation
                        setTimeout(function() {
                            if (alert && alert.parentNode && document.contains(alert)) {
                                alert.remove();
                                // Check if container should be removed after alert removal
                                checkAndRemoveContainer();
                            }
                        }, 300);
                    }
                } catch (error) {
                    // Silently handle any errors to prevent page refresh
                    console.log('Alert dismiss error:', error);
                }
            }, dismissTime);

            // Store timeout ID for potential cancellation
            alert.dataset.timeoutId = timeoutId;

            // Pause auto-dismiss on hover
            alert.addEventListener('mouseenter', function() {
                progressBar.style.animationPlayState = 'paused';
            });

            // Resume auto-dismiss when mouse leaves
            alert.addEventListener('mouseleave', function() {
                progressBar.style.animationPlayState = 'running';
            });

            // Handle manual close button
            const closeButton = alert.querySelector('.btn-close');
            if (closeButton) {
                closeButton.addEventListener('click', function() {
                    // Clear the auto-dismiss timeout
                    if (alert.dataset.timeoutId) {
                        clearTimeout(parseInt(alert.dataset.timeoutId));
                    }

                    // Check if container should be removed after manual close
                    setTimeout(function() {
                        checkAndRemoveContainer();
                    }, 100);
                });
            }
        });
    });
    </script>

    <!-- CSS for progress bar animation -->
    <style>
    @keyframes alertProgress {
        from {
            transform: scaleX(1);
        }
        to {
            transform: scaleX(0);
        }
    }

    .alert {
        transition: opacity 0.3s ease-out, transform 0.3s ease-out;
    }

    .alert.fade:not(.show) {
        opacity: 0;
        transform: translateY(-10px);
    }
    </style>

    {% block extra_scripts %}{% endblock %}
</body>
</html>