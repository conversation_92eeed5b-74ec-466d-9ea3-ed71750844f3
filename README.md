# Vehicle Parking App V1

A comprehensive multi-user parking management system built with Flask, featuring admin and user roles for managing parking lots, spots, and reservations.

## Features

### Admin Features
- **Dashboard**: Overview of all parking lots, spots, and user statistics
- **Parking Lot Management**: Create, edit, and delete parking lots
- **Dynamic Spot Management**: Automatically adjust parking spots based on lot capacity
- **User Management**: View all registered users and their activity
- **Search Functionality**: Search for parking spots by spot number or vehicle number
- **Analytics**: Visual charts showing parking lot occupancy status
- **API Access**: RESTful API endpoints for data access

### User Features
- **Registration & Login**: Secure user authentication
- **Parking Spot Booking**: Book available spots in preferred parking lots
- **Real-time Management**: Check-in and check-out functionality
- **Cost Calculation**: Automatic calculation of parking costs based on duration
- **Personal Dashboard**: View booking history and statistics
- **Analytics**: Personal parking cost history charts

## Technology Stack

- **Backend**: Flask (Python web framework)
- **Database**: SQLite with SQLAlchemy ORM
- **Frontend**: HTML5, CSS3, Bootstrap 5, Jinja2 templating
- **Charts**: Matplotlib for data visualization
- **API**: Flask-RESTful for REST API endpoints
- **Validation**: HTML5 form validation + backend validation
- **Timezone**: pytz for Indian Standard Time (IST) support

## Installation & Setup

### Prerequisites
- Python 3.7 or higher
- pip (Python package installer)

### Installation Steps

1. **Clone or download the project**
   ```bash
   cd Mad1_Vehicle_Parking_App_V1
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**
   ```bash
   python app.py
   ```

4. **Access the application**
   - Open your browser and go to: `http://127.0.0.1:5000`
   - The database will be automatically created on first run

## Default Admin Credentials

- **Username**: `admin`
- **Password**: `admin123`

## Project Structure

```
Mad1_Vehicle_Parking_App_V1/
├── app.py                          # Main Flask application
├── requirements.txt                # Python dependencies
├── README.md                      # Project documentation
├── application/
│   └── controllers.py             # Route handlers and business logic
├── templates/                     # HTML templates
│   ├── base.html                  # Base template with navbar
│   ├── index.html                 # Home page
│   ├── login.html                 # Login form
│   ├── register.html              # Registration form
│   ├── admin_dashboad.html        # Admin dashboard
│   ├── admin_users.html           # Admin user management
│   ├── admin_search.html          # Admin search functionality
│   └── user_dashboard.html        # User dashboard
├── static/
│   └── style.css                  # Custom CSS styles
└── instance/
    └── myproject.db              # SQLite database (auto-created)
```

## Database Schema

### Users Table
- `id` (Primary Key)
- `username` (Unique)
- `email` (Unique)
- `password_hash`
- `is_admin` (Boolean)
- `created_at`

### Parking Lots Table
- `id` (Primary Key)
- `location_name`
- `address`
- `pin_code`
- `price_per_hour`
- `maximum_spots`
- `created_at`

### Parking Spots Table
- `id` (Primary Key)
- `lot_id` (Foreign Key)
- `spot_number`
- `status` (A=Available, O=Occupied)
- `created_at`

### Reservations Table
- `id` (Primary Key)
- `spot_id` (Foreign Key)
- `user_id` (Foreign Key)
- `vehicle_number`
- `parking_timestamp`
- `leaving_timestamp`
- `parking_cost`
- `status` (booked, parked, completed)
- `created_at`

## API Endpoints

- `GET /api/parking_lots` - Get all parking lots
- `GET /api/parking_lots/<id>` - Get specific parking lot details
- `GET /api/parking_spots` - Get all parking spots
- `GET /api/reservations` - Get all reservations

## Key Features Implementation

### 1. Multi-User System
- Role-based access control (Admin vs User)
- Secure password hashing
- Session management

### 2. Dynamic Parking Management
- Automatic spot creation/deletion based on lot capacity
- Real-time availability tracking
- Spot status management

### 3. Cost Calculation
- Time-based pricing using Indian Standard Time (IST)
- Automatic cost calculation on checkout
- Hourly rate configuration per parking lot

### 4. Responsive Design
- Bootstrap 5 for mobile-friendly interface
- Custom CSS for enhanced styling
- Green navbar color (#10b981) as specified

### 5. Form Validation
- HTML5 client-side validation
- Server-side validation with error handling
- Real-time feedback for user inputs

### 6. Data Visualization
- Matplotlib charts for admin analytics
- User-specific parking history charts
- Base64 encoded chart delivery

## Usage Guide

### For Administrators
1. Login with admin credentials
2. Create parking lots with location details and pricing
3. Monitor real-time occupancy through the dashboard
4. Search for specific spots or vehicles
5. View user management and analytics

### For Users
1. Register a new account or login
2. Browse available parking lots
3. Book a spot by selecting lot and entering vehicle number
4. Check-in when arriving at the parking spot
5. Check-out when leaving (cost automatically calculated)
6. View personal parking history and analytics

## Security Features
- Password hashing using Werkzeug
- Session-based authentication
- CSRF protection through form validation
- Input sanitization and validation

## Future Enhancements
- Payment gateway integration
- Email notifications
- Mobile app development
- Advanced reporting features
- Multi-language support

## License
This project is developed for educational purposes as part of the MAD1 course requirements.