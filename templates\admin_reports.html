{% extends "base.html" %}

{% block title %}Admin Reports{% endblock %}

{% block content %}
<div class="container-fluid px-4 py-4">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="bi bi-graph-up me-2"></i>Reports & Analytics
                    </h1>
                    <p class="text-muted mb-0">Comprehensive insights and performance metrics</p>
                </div>
                <div>
                    <button class="btn btn-primary" onclick="window.print()">
                        <i class="bi bi-printer me-2"></i>Print Report
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card shadow h-100 border-start border-primary border-4">
                <div class="card-body">
                    <div class="row g-0 align-items-center">
                        <div class="col me-2">
                            <div class="stats-label text-primary">Total Revenue</div>
                            <div class="stats-number text-primary">₹{{ "%.2f"|format(total_revenue) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-graph-up fs-2 text-primary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card shadow h-100 border-start border-success border-4">
                <div class="card-body">
                    <div class="row g-0 align-items-center">
                        <div class="col me-2">
                            <div class="stats-label text-success">Total Bookings</div>
                            <div class="stats-number text-success">{{ total_bookings }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-calendar-check fs-2 text-success"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card shadow h-100 border-start border-info border-4">
                <div class="card-body">
                    <div class="row g-0 align-items-center">
                        <div class="col me-2">
                            <div class="stats-label text-info">Active Users</div>
                            <div class="stats-number text-info">{{ active_users }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-people fs-2 text-info"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card shadow h-100 border-start border-warning border-4">
                <div class="card-body">
                    <div class="row g-0 align-items-center">
                        <div class="col me-2">
                            <div class="stats-label text-warning">Occupancy Rate</div>
                            <div class="stats-number text-warning">{{ "%.1f"|format(occupancy_rate) }}%</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-pie-chart fs-2 text-warning"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 fw-bold text-primary">Revenue Overview</h6>
                </div>
                <div class="card-body">
                    <div id="revenueChart" class="text-center">
                        {% if revenue_chart %}
                            <img src="data:image/png;base64,{{ revenue_chart }}" class="img-fluid" alt="Revenue Chart">
                        {% else %}
                            <div class="text-center py-4">
                                <i class="bi bi-bar-chart text-muted fs-1"></i>
                                <p class="text-muted mt-2">No revenue data available</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 fw-bold text-primary">Parking Lot Performance</h6>
                </div>
                <div class="card-body">
                    <div id="lotPerformanceChart" class="text-center">
                        {% if performance_chart %}
                            <img src="data:image/png;base64,{{ performance_chart }}" class="img-fluid" alt="Performance Chart">
                        {% else %}
                            <div class="text-center py-4">
                                <i class="bi bi-pie-chart text-muted fs-1"></i>
                                <p class="text-muted mt-2">No performance data available</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Performing Lots -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 fw-bold text-primary">
                        <i class="bi bi-trophy me-2"></i>Top Performing Parking Lots
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>Rank</th>
                                    <th>Location</th>
                                    <th>Total Bookings</th>
                                    <th>Revenue</th>
                                    <th>Avg. Duration</th>
                                    <th>Occupancy Rate</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for lot in top_lots %}
                                <tr>
                                    <td>
                                        <span class="badge bg-primary">{{ loop.index }}</span>
                                    </td>
                                    <td>
                                        <strong>{{ lot.location_name }}</strong><br>
                                        <small class="text-muted">{{ lot.address[:50] }}...</small>
                                    </td>
                                    <td>{{ lot.booking_count }}</td>
                                    <td>₹{{ "%.2f"|format(lot.revenue) }}</td>
                                    <td>{{ "%.1f"|format(lot.avg_duration) }} hrs</td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar" role="progressbar" 
                                                 style="width: {{ lot.occupancy_rate }}%"
                                                 aria-valuenow="{{ lot.occupancy_rate }}" 
                                                 aria-valuemin="0" aria-valuemax="100">
                                                {{ "%.1f"|format(lot.occupancy_rate) }}%
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 fw-bold text-primary">
                        <i class="bi bi-clock-history me-2"></i>Recent Activity
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>Time</th>
                                    <th>User</th>
                                    <th>Vehicle</th>
                                    <th>Location</th>
                                    <th>Action</th>
                                    <th>Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for activity in recent_activities %}
                                <tr>
                                    <td>{{ activity.timestamp.strftime('%Y-%m-%d %H:%M') }}</td>
                                    <td>{{ activity.username }}</td>
                                    <td><code>{{ activity.vehicle_number }}</code></td>
                                    <td>{{ activity.location_name }}</td>
                                    <td>
                                        {% if activity.status == 'completed' %}
                                            <span class="badge bg-success">Completed</span>
                                        {% elif activity.status == 'parked' %}
                                            <span class="badge bg-warning">Parked</span>
                                        {% else %}
                                            <span class="badge bg-info">Booked</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if activity.parking_cost > 0 %}
                                            ₹{{ "%.2f"|format(activity.parking_cost) }}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<!-- No JavaScript needed - charts are rendered server-side -->
{% endblock %}
