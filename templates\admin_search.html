{% extends "base.html" %}

{% block title %}Search Spots - Admin Dashboard{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h3 mb-0">
                <i class="bi bi-search"></i> Search Parking Spots
            </h1>
            <p class="text-muted">Search for parking spots by spot number or vehicle number</p>
        </div>
    </div>

    <!-- Search Form -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-search"></i> Search
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" novalidate>
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="search_query" class="form-label">Search Query</label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="search_query" 
                                           name="search_query" 
                                           placeholder="Enter spot number (e.g., S001) or vehicle number (e.g., MH12AB1234)"
                                           required>
                                    <div class="form-text">Search by parking spot number or vehicle number</div>
                                    <div class="invalid-feedback">Please enter a search query.</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-search"></i> Search
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Search Results -->
    {% if spot %}
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-info-circle"></i> Search Results
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="fw-bold">Parking Spot Information</h6>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Spot Number:</strong></td>
                                    <td>{{ spot.spot_number }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Parking Lot:</strong></td>
                                    <td>{{ spot.parking_lot.location_name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Address:</strong></td>
                                    <td>{{ spot.parking_lot.address }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        {% if spot.status == 'A' %}
                                            <span class="badge bg-success">Available</span>
                                        {% else %}
                                            <span class="badge bg-danger">Occupied</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Price per Hour:</strong></td>
                                    <td>₹{{ spot.parking_lot.price_per_hour }}</td>
                                </tr>
                            </table>
                        </div>
                        
                        {% if spot.status == 'O' and reservation %}
                        <div class="col-md-6">
                            <h6 class="fw-bold">Current Reservation Details</h6>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Vehicle Number:</strong></td>
                                    <td>{{ reservation.vehicle_number }}</td>
                                </tr>
                                <tr>
                                    <td><strong>User:</strong></td>
                                    <td>{{ reservation.user.username }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Email:</strong></td>
                                    <td>{{ reservation.user.email }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        {% if reservation.status == 'booked' %}
                                            <span class="badge bg-warning">Booked</span>
                                        {% elif reservation.status == 'parked' %}
                                            <span class="badge bg-info">Parked</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Booked At:</strong></td>
                                    {% set booking_time = reservation.get_booking_time_ist() %}
                                    <td>{{ booking_time.strftime('%d/%m/%Y %H:%M IST') if booking_time else '-' }}</td>
                                </tr>
                                {% set parking_time = reservation.get_parking_time_ist() %}
                                {% if parking_time %}
                                <tr>
                                    <td><strong>Parked At:</strong></td>
                                    <td>{{ parking_time.strftime('%d/%m/%Y %H:%M IST') }}</td>
                                </tr>
                                {% endif %}
                            </table>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByTagName('form');
        Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>
{% endblock %}
